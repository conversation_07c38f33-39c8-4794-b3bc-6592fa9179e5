import { expect } from 'chai';
import { v4 as uuidv4 } from 'uuid';
import {
  getCountersigners,
  getUserById,
  getUserByUsername,
  getUsers,
  getUsersByRole,
  getValuers,
  saveUser
} from '../../src/services/userService.js';

import { testIds } from './consts.js';
import { STATUS_INVALID, STATUS_MISSING, STATUS_NOT_FOUND } from '../../src/enums/crudStatus.js';
import './setup.js';

describe('getUserById', () => {
  it('should return user when valid UUID is provided', async () => {
    const user = await getUserById(testIds.users.valuer);
    expect(user).to.not.be.null;
    expect(user.id.toLowerCase()).to.equal(testIds.users.valuer.toLowerCase());
    expect(user.name).to.equal('Test User');
  });
  it('should return invalid status when invalid UUID is provided', async () => {
    const result = await getUserById('invalid-uuid');
    expect(result.status).to.equal(STATUS_INVALID);
    expect(result.message).to.equal('Invalid User ID format');
  });
  it('should return not found status when user does not exist', async () => {
    const result = await getUserById(uuidv4());
    expect(result.status).to.equal(STATUS_NOT_FOUND);
    expect(result.message).to.equal('User Not Found');
  });
});

describe('getUserByUsername', () => {
  it('should return not found status when username not provided', async () => {
    const result = await getUserByUsername(null);
    expect(result.status).to.equal(STATUS_MISSING);
    expect(result.message).to.equal('Username must be provided');
  });
  it('should return user when username exists', async () => {
    const user = await getUserByUsername('testuser');
    expect(user).to.not.be.null;
    expect(user.ntUsername).to.equal('testuser');
  });
  it('should return not found status when username does not exist', async () => {
    const result = await getUserByUsername('nonexistent');
    expect(result.status).to.equal(STATUS_NOT_FOUND);
    expect(result.message).to.equal('User Not Found');
  });
});

describe('getUsersByRole', () => {
  it('should return users with specified role', async () => {
    const roleUsers = await getUsersByRole('Valuer');
    expect(roleUsers).to.be.an('array');
    expect(roleUsers.some(u => u.id.toLowerCase() === testIds.users.valuer.toLowerCase())).to.be.true;
    expect(roleUsers.length).to.equal(1);
  });
  it('should return empty array for non-existent role', async () => {
    const roleUsers = await getUsersByRole('NonExistentRole');
    expect(roleUsers).to.be.an('array');
    expect(roleUsers).to.be.empty;
  });
});

describe('getUsers', () => {
  it('should return all user profiles', async () => {
    const users = await getUsers();
    expect(users).to.be.an('array');
    expect(users.length).to.be.at.least(1);

    const valuerUser = users.find(u => u.id.toLowerCase() === testIds.users.valuer.toLowerCase());
    expect(valuerUser).to.not.be.undefined;
    expect(valuerUser.name).to.equal('Test User');
  });
});

describe('getValuers', () => {
  it('should return valuer', async () => {
    const valuers = await getValuers();
    expect(valuers).to.be.an('array');
    expect(valuers.some(v => v.id.toLowerCase() === testIds.users.valuer.toLowerCase())).to.be.true;
    expect(valuers.some(v => v.id.toLowerCase() === testIds.users.customerCare.toLowerCase())).to.be.false;
  });
});

describe('getCountersigners', () => {
  it('should return countersigner', async () => {
    const countersigners = await getCountersigners();
    expect(countersigners).to.be.an('array');
  });
});

describe('saveUser', () => {
  it('should return invalid status when no user is provided', async () => {
    const result = await saveUser(null);
    expect(result.status).to.equal(STATUS_INVALID);
    expect(result.message).to.equal('a valid payload must be provided');
  });

  it('should validate required fields and return errors', async () => {
    const invalidUser = {
      id: testIds.users.customerCare,
      name: '',
      ntUsername: '',
      email: '',
      roles: ['InvalidRole']
    };
    const result = await saveUser(invalidUser);
    expect(result.status).to.equal(STATUS_INVALID);
    expect(result.errors).to.be.an('array');
    expect(result.errors).to.deep.include({ field: 'name', message: 'must be provided' });
    expect(result.errors).to.deep.include({ field: 'ntUsername', message: 'must be provided' });
    expect(result.errors).to.deep.include({ field: 'office', message: 'must be provided' });
    expect(result.errors).to.deep.include({ field: 'email', message: 'must be provided' });
    expect(result.errors).to.deep.include({ field: 'role', message: 'InvalidRole is not a valid role' });
  });

  it('should validate invalid email', async () => {
    const userWithInvalidEmail = {
      id: 'f47ac10b-58cc-4372-a567-0e02b2c3d478',
      name: 'New Test User',
      ntUsername: 'newtestuser',
      qualifications: 'MSc',
      email: 'invalidEmail',
      officeId: testIds.offices[0],
      office: { id: testIds.offices[0] }
    };
    const result = await saveUser(userWithInvalidEmail);
    expect(result.status).to.equal(STATUS_INVALID);
    expect(result.errors).to.be.an('array');
    expect(result.errors).to.deep.include({ field: 'email', message: 'is not a valid email address' });
  });

  it('should validate invalid office', async () => {
    const userWithInvalidOffice = {
      id: 'f47ac10b-58cc-4372-a567-0e02b2c3d478',
      name: 'New Test User',
      ntUsername: 'newtestuser',
      qualifications: 'MSc',
      email: '<EMAIL>',
      officeId: testIds.offices[0],
      office: { id: uuidv4() },
    };
    const result = await saveUser(userWithInvalidOffice);
    expect(result.status).to.equal(STATUS_INVALID);
    expect(result.errors).to.be.an('array');
    expect(result.errors).to.deep.include({ field: 'office', message: 'is not a valid office' });
  });

  it('should validate duplicate name', async () => {
    const duplicateUser = {
      id: 'f47ac10b-58cc-4372-a567-0e02b2c3d478',
      name: 'Test User',
      ntUsername: 'newtestuser',
      qualifications: 'MSc',
      email: '<EMAIL>',
      officeId: testIds.offices[0],
      office: { id: testIds.offices[0] }
    };
    const result = await saveUser(duplicateUser);
    expect(result.status).to.equal(STATUS_INVALID);
    expect(result.errors).to.deep.include({ field: 'name', message: 'must not be the same as another user' });
  });

  it('should validate duplicate ntUsername', async () => {
    const duplicateUser = {
      id: 'f47ac10b-58cc-4372-a567-0e02b2c3d478',
      name: 'New Test User',
      ntUsername: 'testuser',
      qualifications: 'MSc',
      email: '<EMAIL>',
      officeId: testIds.offices[0],
      office: { id: testIds.offices[0] }
    };
    const result = await saveUser(duplicateUser);
    expect(result.status).to.equal(STATUS_INVALID);
    expect(result.errors).to.deep.include({ field: 'ntUsername', message: 'must not be the same as another user' });
  });

  it('should create new user successfully with all fields', async function () {
    const newUser = {
      name: 'New Test User 2',
      ntUsername: 'newtestuser2',
      qualifications: 'MSc',
      email: '<EMAIL>',
      office: { id: testIds.offices[0] },
      roles: ['Valuer']
    };

    const result = await saveUser(newUser);
    expect(result).to.not.be.null;
    expect(result.id).to.not.be.null;
    expect(result.name).to.equal('New Test User 2');
    expect(result.roles).to.include('Valuer');
  });

  it('should update existing user successfully with all fields', async function () {
    const newUser = {
      id: 'a3083e1d-722b-4179-b4e7-d75e1f23d3a6',
      name: 'Updated Test User',
      ntUsername: 'newtestuser',
      qualifications: 'MSc',
      email: '<EMAIL>',
      office: { id: testIds.offices[0] },
      roles: ['Valuer']
    };

    const result = await saveUser(newUser);
    expect(result).to.not.be.null;
    expect(result.id.toLowerCase()).to.equal(newUser.id);
    expect(result.name).to.equal('Updated Test User');
  });
});
