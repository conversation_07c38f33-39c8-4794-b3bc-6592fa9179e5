
CREATE PROCEDURE [dbo].[spd_MONARCH_searchUsers](
  @criteriaXml xml,
@pageSize integer,
@offset integer,
@orderBy varchar(30) = 'id',
@desc bit = 0
)
AS
BEGIN
SET NOCOUNT ON;

/* -----------------
   Users
   -----------------

   -- name
   -- ntUsername
   -- office
    -- name
    -- phoneNumber
    -- faxNumber
    -- addressLine1
    -- addressLine2
    -- addressLine3
    -- addressLine4
    -- addressLine5
    -- postCode
   -- qualifications
   -- email
   -- isRegisteredValuer (in Senior Valuer, Managing Senior Valuer, Area Valuer)
   -- isValuer (in Senior Valuer, Managing Senior Valuer, Area Valuer, Valuer)

   -- NOT RETURNING THESE AS THIS IS A HACK IN MONARCH AND QIVS AT THE MOMENT
   -- roles
   -- isAdmin (receptionist typist)
*/

-- ------------
  -- GET CRITERIA
-- ------------
  declare @isValuer as bit
set @isValuer = case @criteriaXml.value('/criteria[1]/is_valuer_yn[1]', 'varchar(10)')
when 'true' then 1
when 'false' then 0
else null end

declare @isRegisteredValuer as bit
set @isRegisteredValuer = case @criteriaXml.value('/criteria[1]/is_registered_valuer_yn[1]', 'varchar(10)')
when 'true' then 1
when 'false' then 0
else null end

declare @isActive as bit
set @isActive = case @criteriaXml.value('/criteria[1]/is_active_yn[1]', 'varchar(10)')
when 'true' then 1
when 'false' then 0
else null end

declare @username as varchar(100)
set @username = nullif(@criteriaXml.value('/criteria[1]/username[1]', 'varchar(100)'),'')

-- sort direction
declare @direction as int
set @direction = case @desc when 1 then -1 else 1 end

-- Need to be able to sort by ...
declare @orderCol as int
set @orderCol = case @orderBy
when 'id' then 1
when 'name' then 2
else 0
end

-- holding table
declare @pageRows as table(row_nbr int, id int, found_count int);

-- ---------
  -- Using CTE gains an epic performance improvement compared to count(*) over ()
-- https://stackoverflow.com/questions/12352471/getting-total-row-count-from-offset-fetch-next
-- ---------

with foundRows as (
  select case @orderCol
-- id
when 1 then cast(u.qv_user_id as varbinary)
-- name
when 2 then cast(e.full_name as varbinary)
else null
end
as 'order_value'
  , qv_user_id as 'id'
from	qv_user u left join employee e on u.employee_id = e.employee_id
where (		 (select	isnull(max(1),0)
from		employee e join employee_group_type eg on e.employee_group_id = eg.employee_group_id
where		e.employee_id = u.employee_id and eg.is_valuer = 1) = @isValuer
or @isValuer is null
)
and   (		 (select	isnull(max(1),0)
from		employee e join employee_group_type eg on e.employee_group_id = eg.employee_group_id
where		e.employee_id = u.employee_id and eg.code <> 3 and eg.is_valuer = 1) = @isRegisteredValuer
or @isRegisteredValuer is null
)
and (isnull(u.active,0) = @isActive or @isActive is null)
and	(u.qv_user = @username or @username is null)
)
insert into @pageRows
select row_nbr
  , id
  , found_count
from (	select   y.row_nbr
  ,y.id
  ,max(row_nbr) over() as found_count
from (
  select 	row_number() over(order by order_value) row_nbr
  , 	id
from	foundRows
)y
) z
where	(@desc = 0 and row_nbr >= @offset + 1 and row_nbr <= @offset + @pageSize)
OR		(@desc = 1 and row_nbr <= found_count - @offset and row_nbr >= found_count - @offset - @pageSize + 1)

select	dbo.fn_MONARCH_getUser(id) as 'user_xml'
  , found_count
from	@pageRows
order by row_nbr * @direction

END;


/*
exec spd_monarch_searchUsers null,50,0

declare @criteria as xml
set @criteria = cast('<criteria><is_valuer_yn>true</is_valuer_yn></criteria>' as xml)
exec spd_monarch_searchUsers @criteria,50,0

declare @criteria as xml
set @criteria = cast('<criteria><is_registered_valuer_yn>true</is_registered_valuer_yn></criteria>' as xml)
exec spd_monarch_searchUsers @criteria,50,0

declare @criteria as xml
set @criteria = cast('<criteria><is_registered_valuer_yn>true</is_registered_valuer_yn><is_active_yn>true</is_active_yn></criteria>' as xml)
exec spd_monarch_searchUsers @criteria,50,0, 'name'

declare @criteria as xml
set @criteria = cast('<criteria><username>QVNZ-STEWARTM</username></criteria>' as xml)
exec spd_monarch_searchUsers @criteria,50,0, 'name'

*/

