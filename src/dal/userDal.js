import { getQivsPool } from './sqlConnectionPool.js';
import mssql from 'mssql';
import { User } from '../models/user.js';

const USER_QUERY = `
    SELECT u.id, u.name, u.nt_username, u.office_id, u.qualifications, u.email, u.target_role, u.h_target_role, r.role,
           o.name as office_name, o.phone_number, o.fax_number, o.address_line_1, o.address_line_2, o.address_line_3,
           o.address_line_4, o.address_line_5, o.post_code
    FROM monarch.[user] u
    LEFT JOIN monarch.office o ON o.id = u.office_id
    LEFT JOIN monarch.user_role ur ON ur.user_id = u.id
    LEFT JOIN monarch.role r ON r.id = ur.role_id
`;

export async function getUserById(id) {
  const pool = await getQivsPool();
  const query = `${USER_QUERY} WHERE u.id = @id`;

  try {
    const result = await pool.request().input('id', mssql.VarChar, id).query(query);
    if (result.recordset.length === 0) {
      return null;
    }
    return User.fromDatabaseRecordset(result.recordset);
  } catch (error) {
    logger.error('ERR-USR-301', 'Error fetching user profile:', error);
    throw new Error('Failed to fetch user profile');
  }
}

export async function getUserByUsername(username) {
  const pool = await getQivsPool();
  const query = `${USER_QUERY} WHERE u.nt_username = @username`;
  try {
    const result = await pool.request().input('username', mssql.VarChar, username).query(query);
    if (result.recordset.length === 0) {
      return null;
    }

    return User.fromDatabaseRecordset(result.recordset);
  } catch (error) {
    logger.error('ERR-USR-302', 'Error fetching user profile by username:', error);
    throw new Error('Failed to fetch user profile by username');
  }
}

export async function getUsersByRole(role) {
  const pool = await getQivsPool();
  const query = `
    SELECT u.id, u.name, u.nt_username, u.office_id, u.qualifications, u.email, u.target_role, u.h_target_role, r.role,
           o.name as office_name, o.phone_number, o.fax_number, o.address_line_1, o.address_line_2, o.address_line_3,
           o.address_line_4, o.address_line_5, o.post_code
    FROM monarch.[user] u
    LEFT JOIN monarch.office o ON o.id = u.office_id
    LEFT JOIN monarch.user_role ur ON ur.user_id = u.id
    LEFT JOIN monarch.role r ON r.id = ur.role_id
    WHERE r.role = @role
    ORDER BY u.name ASC
  `;

  try {
    const result = await pool.request()
      .input('role', mssql.VarChar, role)
      .query(query);

    return User.fromDatabaseRecordsets(result.recordset);
  } catch (error) {
    logger.error('ERR-USR-303', 'Error fetching users by role:', error);
    throw new Error('Failed to fetch users by role');
  }
}


export async function getUsers() {
  const pool = await getQivsPool();
  const query = `${USER_QUERY} ORDER BY u.name ASC`;

  try {
    const result = await pool.request().query(query);
    return User.fromDatabaseRecordsets(result.recordset);
  } catch (error) {
    logger.error('ERR-USR-304', 'Error fetching user profiles:', error);
    throw new Error('Failed to fetch user profiles');
  }
}

export async function getValuerUsers() {
  const pool = await getQivsPool();
  const query = `${USER_QUERY} WHERE r.role LIKE '%Valuer%' ORDER BY u.name ASC`;

  try {
    const result = await pool.request().query(query);
    return User.fromDatabaseRecordsets(result.recordset);
  } catch (error) {
    logger.error('ERR-USR-305', 'Error fetching valuer profiles:', error);
    throw new Error('Failed to fetch valuer profiles');
  }
}

export async function getCountersignerUsers() {
  const pool = await getQivsPool();
  const query = `${USER_QUERY} WHERE r.role = 'Registered Valuer' ORDER BY u.name ASC`;

  try {
    const result = await pool.request().query(query);
    return User.fromDatabaseRecordsets(result.recordset);
  } catch (error) {
    logger.error('ERR-USR-306', 'Error fetching countersigners profiles:', error);
    throw new Error('Failed to fetch countersigners profiles');
  }
}

export async function getRatingValuerUsers() {
  const pool = await getQivsPool();
  const query = `${USER_QUERY} WHERE r.role LIKE '%Valuer%' ORDER BY u.name ASC`;

  try {
    const result = await pool.request().query(query);
    return User.fromDatabaseRecordsets(result.recordset);
  } catch (error) {
    logger.error('ERR-USR-307', 'Error fetching rating valuers:', error);
    throw new Error('Failed to fetch rating valuers');
  }
}

export async function saveUser(user) {
  const pool = await getQivsPool();

  try {
    const checkQuery = `
        SELECT 1 FROM monarch.[user] WHERE id = @id
    `;

    const insertQuery = `
      INSERT INTO monarch.[user] (id, name, nt_username, office_id, qualifications, email, target_role, h_target_role)
      VALUES (@id, @name, @nt_username, @office_id, @qualifications, @email, @target_role, @h_target_role)
    `;

    const updateQuery = `
    UPDATE monarch.[user]
    SET
      name = @name,
      nt_username = @nt_username,
      office_id = @office_id,
      qualifications = @qualifications,
      email = @email,
      target_role = @target_role,
      h_target_role = @h_target_role
    WHERE id = @id
  `;

    const request = await pool
      .request()
      .input('id', mssql.VarChar, user.id)
      .input('name', mssql.VarChar, user.name?.trim())
      .input('nt_username', mssql.VarChar, user.ntUsername?.trim())
      .input('office_id', mssql.VarChar, user.office?.id)
      .input('qualifications', mssql.VarChar, user.qualifications?.trim())
      .input('email', mssql.VarChar, user.email?.trim())
      .input('target_role', mssql.VarChar, user.target?.trim())
      .input('h_target_role', mssql.VarChar, user.prevTarget?.trim());
    const checkResult = await request.query(checkQuery);
    if (checkResult.recordset.length > 0) {
      await request.query(updateQuery);
    } else {
      await request.query(insertQuery);
    }

    await pool
      .request()
      .input('user_id', mssql.VarChar, user.id)
      .query('DELETE FROM monarch.user_role WHERE user_id = @user_id');

    if (user.roles?.length > 0) {
      const insertRoleQuery = `
        INSERT INTO monarch.user_role (user_id, role_id)
        SELECT @user_id, id FROM monarch.role WHERE role = @role
      `;

      for (const role of user.roles) {
        await pool
          .request()
          .input('user_id', mssql.VarChar, user.id)
          .input('role', mssql.VarChar, role)
          .query(insertRoleQuery);
      }
    }

    const result = await pool
      .request()
      .input('id', mssql.VarChar, user.id)
      .query(`${USER_QUERY} WHERE u.id = @id`);

    if (result.recordset.length === 0) {
      return null;
    }

    return User.fromDatabaseRecordset(result.recordset);
  } catch (error) {
    logger.error('ERR-USR-308', 'Error saving user:', error);
    throw new Error('Failed to save user');
  }
}

export async function validateUserName(name, id) {
  const pool = await getQivsPool();
  try {
    const result = await pool
      .request()
      .input('name', mssql.VarChar, name)
      .input('id', mssql.VarChar, id)
      .query('SELECT 1 FROM monarch.[user] WHERE name = @name AND id != @id');
    return result.recordset.length > 0;
  } catch (error) {
    logger.error('ERR-USR-309', 'Error validating user name:', error);
    throw new Error('Failed to validate user name');
  }
}

export async function validateUserUsername(username, id) {
  const pool = await getQivsPool();
  try {
    const result = await pool
      .request()
      .input('nt_username', mssql.VarChar, username)
      .input('id', mssql.VarChar, id)
      .query('SELECT 1 FROM monarch.[user] WHERE nt_username = @nt_username AND id != @id');
    return result.recordset.length > 0;
  } catch (error) {
    logger.error('ERR-USR-310', 'Error validating user username:', error);
    throw new Error('Failed to validate user username');
  }
}