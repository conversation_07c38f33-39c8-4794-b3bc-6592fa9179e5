USE [devdone_qvnz]
GO

/****** Object:  UserDefinedFunction [dbo].[fn_MONARCH_getUser]    Script Date: 26/05/2025 10:36:36 pm ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE FUNCTION [dbo].[fn_M<PERSON>ARCH_getUser](@id as int) returns xml
as
begin
	declare @xml as xml

	/* -----------------
	   Users
	   -----------------

	   -- name
	   -- ntUsername
	   -- office
			-- name
			-- phoneNumber
			-- faxNumber
			-- addressLine1
			-- addressLine2
			-- addressLine3
			-- addressLine4
			-- addressLine5
			-- postCode
	   -- qualifications
	   -- roles
	   -- email
	   -- isRegistered<PERSON><PERSON><PERSON> (in Senior Valuer, Managing Senior Valuer, Area Valuer)
	   -- is<PERSON><PERSON><PERSON> (in Senior Valuer, Managing Senior Valuer, Area Valuer, Valuer)
	   -- isAd<PERSON> (receptionist typist)
	*/

	set @xml =
	(select   u.qv_user_id	as 'id'
			, u.qv_user		as 'username'
			, e.nt_user_name
			, e.full_name	as 'name'
			, e.email_address
			, e.qualifications
			, e.code		as 'employee_code'
			, u.last_login	as 'last_login_date'
			, case when u.active = 1 and len(qv_user) > 0 then 'true' else 'false' end	as 'is_active_yn'
			, case when u.locked_out = 1 then 'true' else 'false' end	as 'is_locked_out_yn'
			, case when u.must_change_password = 1 then 'true' else 'false' end	as 'must_change_password_yn'
			, case when u.cannot_change_password = 1 then 'true' else 'false' end	as 'cannot_change_password_yn'
			, u.failed_logins  as 'failed_logins_count'
			, case when exists(select 1 from employee_group_type eg where eg.employee_group_id = e.employee_group_id and is_valuer = 1)
			  then 'true' else 'false' end as is_valuer_yn
			, case when exists(select 1 from employee_group_type eg where eg.employee_group_id = e.employee_group_id and is_valuer = 1 and eg.code <> '3' )
			  then 'true' else 'false' end as is_registered_valuer_yn
			, o.qv_office_id			as 'office/id'
			, rtrim(o.qv_office_code)	as 'office/code'
			, rtrim(o.qv_office_name)	as 'office/name'
			, rtrim(o.phone_number_day) as 'office/phone_number'
			, rtrim(o.fax_number)		as 'office/fax_number'
			, rtrim(o.line_1_text)		as 'office/address_line_1'
			, rtrim(o.line_2_text)		as 'office/address_line_2'
			, rtrim(o.line_3_text)		as 'office/address_line_3'
			, rtrim(o.line_4_text)		as 'office/address_line_4'
			, rtrim(o.line_5_text)		as 'office/address_line_5'
			, rtrim(o.post_code)		as 'office/post_code'
	 from qv_user u left join employee e on u.employee_id = e.employee_id
	 left join qv_office o on e.qv_office_id = o.qv_office_id
	 where u.qv_user_id = @id
	 for xml path ('user'), TYPE, ELEMENTS
	)

	return @xml

end





GO


